#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录窗口界面
PyQt5主界面，包含账号管理和登录功能
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QLineEdit, QPushButton, QComboBox,
                             QTextEdit, QMessageBox, QGroupBox, QFormLayout,
                             QSplitter, QFrame, QDateEdit, QFileDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QIcon
from account_manager import AccountManager
import traceback
from DrissionPage import ChromiumPage, ChromiumOptions, SessionPage
import time
import requests
import json
import urllib.parse
from datetime import datetime, timedelta
from enum import Enum
import tempfile
import uuid
import pandas as pd
import os
import tempfile
import uuid
from openpyxl import load_workbook
from openpyxl.styles import Font, Alignment
from openpyxl.utils import get_column_letter


class TableFlag(Enum):
    """表格标志枚举"""
    NO = "0"   # 选择否
    YES = "1"  # 选择是

    @classmethod
    def get_description(cls, value):
        """获取枚举值的描述"""
        descriptions = {
            cls.NO.value: "否",
            cls.YES.value: "是"
        }
        return descriptions.get(value, "未知")


class IEFlag(Enum):
    """进出口标志枚举"""
    EXPORT = "E"  # 出口
    IMPORT = "I"  # 进口

    @classmethod
    def get_description(cls, value):
        """获取枚举值的描述"""
        descriptions = {
            cls.EXPORT.value: "出口",
            cls.IMPORT.value: "进口"
        }
        return descriptions.get(value, "未知")


class EtpsCategory(Enum):
    """企业类型枚举"""
    COMPANY = "C"      # 企业
    INDIVIDUAL = "I"   # 个人

    @classmethod
    def get_description(cls, value):
        """获取枚举值的描述"""
        descriptions = {
            cls.COMPANY.value: "企业",
            cls.INDIVIDUAL.value: "个人"
        }
        return descriptions.get(value, "未知")


# 字段映射配置
FIELD_MAPPING = {
    '统一编号': 'cusCiqNo',
    '海关编号': 'entryId',
    '境内收发货人': 'consignorCname',
    '境内收发货人18位社会信用代码': 'cnsnTradeScc',
    '提运单号': 'billNo',
    '进出口日期': 'iEDate',
    '监管方式': 'supvModeCddeName',
    '合同协议号': 'contrNo',
    '商品项数': 'goodsNum',
    '运输工具名称': 'trafName',
    '航次号': 'cusVoyageNo',
    '报关状态': 'cusDecStatusName',
    '是否查验': 'chktstFlag',
    '申报单位名称': 'agentName',
    '进出口标志': 'cusIEFlagName',
    '申报地海关': 'customMasterName',
    '入境/离境口岸': 'despPortCodeName',
    '集装箱数量': 'contaCount',
}


def apply_excel_style(output_file: str) -> None:
    """应用Excel样式"""
    wb = load_workbook(output_file)

    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]

        # 设置标题栏样式（首行）
        header_font = Font(name='等线', bold=True, size=12)
        header_alignment = Alignment(wrap_text=True, vertical='center')

        if ws.max_row >= 1:
            for cell in ws[1]:
                cell.font = header_font
                cell.alignment = header_alignment

        # 设置内容栏样式
        content_font = Font(name='等线', size=11)
        content_alignment = Alignment(wrap_text=False, vertical='center')

        for row in ws.iter_rows(min_row=2):
            for cell in row:
                cell.font = content_font
                cell.alignment = content_alignment

        # 自动调整列宽（已修复部分）
        for col_idx, column in enumerate(ws.columns, 1):
            max_length = 0
            column_letter = get_column_letter(col_idx)  # 使用新方法获取列字母
            for cell in column:
                try:
                    value_length = len(str(cell.value))
                    max_length = max(max_length, value_length)
                except:
                    pass
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column_letter].width = adjusted_width

    wb.save(output_file)


def getsetime():
    """
    获取默认的开始时间和结束时间
    返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"

    Returns:
        tuple: (开始日期, 结束日期) 格式为 "YYYY-MM-DD"
    """
    from datetime import datetime, timedelta
    current_date = datetime.now()
    current_weekday = current_date.weekday()
    previous_friday_index = current_weekday - 4
    if previous_friday_index < 0:
        previous_friday_index += 7
    previous_friday = current_date - timedelta(days=previous_friday_index)
    previous_six_days = previous_friday - timedelta(days=6)
    # 返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    return previous_six_days.strftime("%Y-%m-%d"), previous_friday.strftime("%Y-%m-%d")


def extract_cus_ciq_nos(data):
    """
    从数据中提取所有的cusCiqNo

    :param data: 报关单数据（JSON格式）
    :return: cusCiqNo列表
    """
    try:
        # 解析数据
        if isinstance(data, str):
            data = json.loads(data)

        rows = data.get('rows', [])
        if not rows:
            return []

        # 提取所有的cusCiqNo
        cus_ciq_nos = []
        for row in rows:
            cus_ciq_no = row.get('cusCiqNo', '')
            if cus_ciq_no and cus_ciq_no not in cus_ciq_nos:
                cus_ciq_nos.append(cus_ciq_no)

        return cus_ciq_nos

    except Exception as e:
        print(f"提取cusCiqNo失败: {str(e)}")
        return []


def get_pdf(cusCiqNo, page, pdfpath):
    """
    下载pdf
    :param cusCiqNo: 获取所有符合条件的统一编号
    :param page: 页面对象
    :param pdfpath: PDF保存路径
    :return: None
    """
    # 打印输入的统一编号列表
    print("需要下载编号:", cusCiqNo)

    ourl = "https://sz.singlewindow.cn/dyck/swProxy/decserver/sw/dec/printCluster/entries/ftl/1/0/0/"
    # 遍历每个统一编号
    for item in cusCiqNo:
        # 构建下载URL
        url = ourl + item + ".pdf"
        # 下载PDF文件到指定路径
        page.download(url, goal_path=pdfpath, timeout=20)
        # 等待1秒，以避免频繁下载
        time.sleep(1)
    # 等待10秒，以确保所有文件下载完成
    time.sleep(10)


def export_to_excel(data, filename=None, save_path=None):
    """
    将报关单数据导出到Excel文件

    :param data: 报关单数据（JSON格式）
    :param filename: 输出文件名（可选）
    :param save_path: 保存路径（可选）
    :return: 导出的文件路径
    """
    try:
        # 解析数据
        if isinstance(data, str):
            data = json.loads(data)

        rows = data.get('rows', [])
        if not rows:
            raise ValueError("没有数据可导出")

        # 创建DataFrame
        export_data = []
        for row in rows:
            mapped_row = {}
            for chinese_name, field_name in FIELD_MAPPING.items():
                # 获取字段值，如果不存在则为空字符串
                value = row.get(field_name, '')
                # 处理None值
                if value is None:
                    value = ''
                mapped_row[chinese_name] = value
            export_data.append(mapped_row)

        df = pd.DataFrame(export_data)

        # 生成文件名
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"报关单数据_{timestamp}.xlsx"

        # 处理保存路径
        if save_path and os.path.exists(save_path):
            full_path = os.path.join(save_path, filename)
        else:
            full_path = filename

        # 导出到Excel
        df.to_excel(full_path, index=False, engine='openpyxl')

        # 应用Excel样式
        apply_excel_style(full_path)

        return full_path

    except Exception as e:
        raise Exception(f"Excel导出失败: {str(e)}")

class LoginThread(QThread):
    """登录线程，用于执行DrissionPage登录操作"""

    # 定义信号
    login_success = pyqtSignal(str)  # 登录成功信号
    login_failed = pyqtSignal(str)   # 登录失败信号
    login_progress = pyqtSignal(str) # 登录进度信号
    data_received = pyqtSignal(str)  # 数据接收信号
    
    def __init__(self, account_info, start_date="", end_date="", account_name=""):
        super().__init__()
        self.account_info = account_info
        self.start_date = start_date
        self.end_date = end_date
        self.account_name = account_name
    
    def run(self):
        """执行登录操作"""
        try:
            self.login_progress.emit("正在初始化浏览器...")
            


            # 获取账号信息
            password = self.account_info.get('password', '')
            account_name = self.account_name

            self.login_progress.emit(f"正在使用账号 {account_name} 登录...")
            self.login_progress.emit(f"查询日期范围: {self.start_date} 到 {self.end_date}")

            # 执行登录逻辑
            page = self.login_to_system(password)

            if page:
                self.login_success.emit(f"✅ 账号 {account_name} 登录成功！已跳转到报关数据查询页面")

                # 获取浏览器cookies并进行数据查询
                self.fetch_data_with_cookies(page, self.start_date, self.end_date)
            else:
                self.login_failed.emit(f"❌ 账号 {account_name} 登录失败")
            
        except Exception as e:
            error_msg = f"❌ 登录过程中发生错误: {str(e)}"
            self.login_failed.emit(error_msg)

    def login_to_system(self, password):
        """
        登录并跳转到报关数据查询
        """
        max_retries = 3
        for attempt in range(max_retries):
            page = None
            try:
                if attempt > 0:
                    self.login_progress.emit(f"🔄 第 {attempt + 1} 次登录尝试...")

                return self._perform_login(password, attempt + 1)

            except Exception as e:
                self.login_progress.emit(f"❌ 第 {attempt + 1} 次登录尝试失败: {str(e)}")
                if attempt < max_retries - 1:
                    self.login_progress.emit(f"⏳ 等待5秒后进行第 {attempt + 2} 次重试...")
                    time.sleep(5)  # 等待5秒后重试
                else:
                    self.login_progress.emit(f"❌ 已达到最大重试次数({max_retries})，登录失败")
                    return None

        return None

    def _perform_login(self, password, attempt_num):
        """
        执行单次登录尝试
        """
        page = None
        try:
            # 创建ChromiumOptions配置
            self.login_progress.emit("正在配置浏览器选项（无痕模式）...")
            co = ChromiumOptions()

            # 启用无痕模式，避免账号间相互影响
            co.incognito(True)  # 启用无痕模式

            # 其他浏览器选项
            co.set_argument('--disable-web-security')  # 禁用web安全
            co.set_argument('--disable-features=VizDisplayCompositor')  # 禁用某些功能
            co.set_argument('--no-sandbox')  # 禁用沙盒模式
            co.set_argument('--disable-dev-shm-usage')  # 禁用/dev/shm使用
            co.set_argument('--disable-blink-features=AutomationControlled')  # 禁用自动化控制检测

            # 每次都使用新的用户数据目录，确保完全隔离
            temp_dir = tempfile.mkdtemp(prefix=f"chrome_profile_{uuid.uuid4().hex[:8]}_")
            co.set_user_data_path(temp_dir)

            self.login_progress.emit(f"使用临时用户目录: {temp_dir}")
            self.login_progress.emit("✅ 无痕模式已启用，账号间完全隔离")

            # 创建ChromiumPage对象
            self.login_progress.emit("正在创建浏览器页面...")
            page = ChromiumPage(addr_or_opts=co)

            # 访问中国国际贸易单一窗口
            http_path = 'https://app.singlewindow.cn/cas/login?service=https%3A%2F%2Fsz.singlewindow.cn%2Fdyck%2FswProxy%2Fdeskserver%2Fsw%2FdeskIndex%3Fmenu_id%3Ddec001'  # 登录页面地址
            self.login_progress.emit(f"正在访问: {http_path}")
            page.get(http_path)

            self.login_progress.emit(f"页面标题: {page.title}")
            if page.title == "中国国际贸易单一窗口登录管理":
                # 等待并点击卡介质密码登录的按钮
                self.login_progress.emit("正在切换到卡介质密码登录...")
                page.ele("@id=cardTabBtn").wait.has_rect()
                page.ele("@id=cardTabBtn").click()

                # 等待并输入卡介质密码
                self.login_progress.emit("正在输入卡介质密码...")
                page.ele("@id=password").wait.has_rect()
                page.ele("@id=password").input(password)
                time.sleep(1)

                # 等待并点击 Intel 选项框
                self.login_progress.emit("正在选择Intel选项...")
                page.ele("@id=checkboxIntel").wait.has_rect()
                page.ele("@id=checkboxIntel").click()
                time.sleep(1)

                # 等待并点击登录按钮
                self.login_progress.emit("正在点击登录按钮...")
                page.ele("@id=loginbutton").wait.has_rect()
                page.ele("@id=loginbutton").click()

                # 等待登录成功标志元素出现
                self.login_progress.emit(f"等待登录成功确认（第{attempt_num}次尝试）...")
                try:
                    # 等待"企业操作员"元素出现，最多等待30秒
                    success_element = page.ele("@id=accountTypeCn", timeout=30)
                    time.sleep(2)
                    if success_element and success_element.text == "企业操作员":
                        self.login_progress.emit("✅ 检测到登录成功标志：企业操作员")
                    else:
                        # 登录状态异常，抛出异常以触发重试
                        error_msg = "登录状态异常，未检测到企业操作员标志"
                        self.login_progress.emit(f"⚠️ {error_msg}")
                        raise Exception(error_msg)
                except Exception as e:
                    if "登录状态异常" in str(e):
                        # 重新抛出登录状态异常，触发重试
                        raise e
                    else:
                        # 其他异常（如超时），也触发重试
                        error_msg = f"等待登录确认时出错: {str(e)}"
                        self.login_progress.emit(f"⚠️ {error_msg}")
                        raise Exception(error_msg)

            return page

        except Exception as e:
            self.login_progress.emit(f"登录执行失败: {e}")
            if page:
                try:
                    page.quit()
                except:
                    pass
            return None

        finally:
            # 清理临时用户目录（如果需要的话）
            # 注意：这里不清理，因为page对象还在使用中
            # 清理工作会在数据获取完成后进行
            pass

    def create_custom_query(self, start_date=None, end_date=None, table_flag=None, ie_flag=None, etps_category=None):
        """
        创建自定义查询参数

        :param start_date: 开始时间
        :param end_date: 结束时间
        :param table_flag: 表格标志 (使用 TableFlag 枚举)
        :param ie_flag: 进出口标志 (使用 IEFlag 枚举)
        :param etps_category: 企业类型 (使用 EtpsCategory 枚举)
        :return: 编码后的查询参数
        """

        # 如果没有指定日期，使用传入的日期
        if not start_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")

        # 使用枚举设置默认值
        if table_flag is None:
            table_flag = TableFlag.YES.value  # 默认选择是
        if ie_flag is None:
            ie_flag = IEFlag.EXPORT.value  # 默认出口
        if etps_category is None:
            etps_category = EtpsCategory.COMPANY.value  # 默认企业

        # 定义数据字典
        data = {
            "cusCiqNoHidden": "",
            "dclTrnRelFlagHidden": "",
            "transPreNoHidden": "",
            "cusIEFlagHidden": "",
            "cusOrgCode": "",
            "dclTrnRelFlag": "0",
            "cusDecStatus": "",
            "etpsCategory": etps_category,
            "cusIEFlag": ie_flag,
            "entryId": "",
            "cusCiqNo": "",
            "cnsnTradeCode": "",
            "billNo": "",
            "customMaster": "",
            "tableFlag": table_flag,
            "updateTime": start_date,
            "updateTimeEnd": end_date,
            "operateDate": "1",
            "verifyCode": "",
            "queryPage": "cusBasicQuery",
            "operType": "0"
        }
        # 将数据转换为JSON字符串
        json_str = json.dumps(data)

        # 执行两次encodeURI操作
        encoded_once = urllib.parse.quote(json_str, safe='~()*!.\'')
        encoded_twice = urllib.parse.quote(encoded_once, safe='~()*!.\'')

        return encoded_twice

    def fetch_data_with_cookies(self, page, start_date, end_date):
        """使用浏览器cookies获取数据"""
        try:
            self.login_progress.emit("正在获取浏览器cookies...")

            # 获取浏览器cookies - 使用正确的DrissionPage API
            try:
                # 使用正确的 cookies() 方法
                cookies = page.cookies()

                # 转换cookies格式为requests可用的格式
                cookies_dict = {}

                # DrissionPage的CookiesList可以直接迭代
                for cookie in cookies:
                    # 每个cookie应该有name和value属性
                    if hasattr(cookie, 'name') and hasattr(cookie, 'value'):
                        cookies_dict[cookie.name] = cookie.value
                    elif isinstance(cookie, dict):
                        cookies_dict[cookie.get('name', '')] = cookie.get('value', '')

                self.login_progress.emit(f"转换后的cookies数量: {len(cookies_dict)}")

                # 如果没有获取到cookies，直接报错
                if not cookies_dict:
                    raise Exception("未获取到有效的cookies")

            except Exception as cookie_error:
                self.login_progress.emit(f"❌ 获取cookies失败: {str(cookie_error)}")
                return  # 直接返回，不继续执行

            self.login_progress.emit(f"正在查询数据: {start_date} 到 {end_date}")

            # 设置请求头
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Content-Type': 'application/json',
                'Pragma': 'no-cache',
                'Referer': 'https://sz.singlewindow.cn/dyck/swProxy/decserver/sw/dec/cusQueryZhNew?ngBasePath=https%3A%2F%2Fsz.singlewindow.cn%3A443%2Fdyck%2FswProxy%2Fdecserver%2F',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }


            # 执行双重查询（table_flag为是和否）并合并结果
            all_merged_data = self.execute_dual_query(start_date, end_date, headers, cookies_dict)

            # 处理双重查询结果
            if all_merged_data:
                self.login_progress.emit("✅ 双重查询数据获取成功，正在处理数据...")
                self.data_received.emit(json.dumps(all_merged_data, ensure_ascii=False))

                # 在数据获取成功后，立即下载PDF
                try:
                    self.login_progress.emit("📄 正在提取统一编号...")
                    cus_ciq_nos = extract_cus_ciq_nos(all_merged_data)

                    if cus_ciq_nos:
                        self.login_progress.emit(f"✅ 提取到 {len(cus_ciq_nos)} 个统一编号")

                        # 创建PDF保存路径
                        save_path = os.getcwd()  # 使用当前目录作为默认路径
                        pdf_path = os.path.join(save_path, "pdf")
                        if not os.path.exists(pdf_path):
                            os.makedirs(pdf_path)
                            self.login_progress.emit(f"📁 已创建PDF保存目录: {pdf_path}")

                        self.login_progress.emit("📄 开始下载PDF文件...")
                        get_pdf(cus_ciq_nos, page, pdf_path)
                        self.login_progress.emit("✅ PDF下载完成")

                    else:
                        self.login_progress.emit("⚠️ 未找到统一编号，跳过PDF下载")

                except Exception as pdf_error:
                    self.login_progress.emit(f"❌ PDF下载失败: {str(pdf_error)}")

            else:
                self.login_progress.emit("❌ 未获取到任何数据")

        except Exception as e:
            self.login_progress.emit(f"❌ 数据获取过程中发生错误: {str(e)}")

        finally:
            # 数据获取完成后，清理浏览器和临时目录
            self.cleanup_browser_session(page)

    def execute_dual_query(self, start_date, end_date, headers, cookies_dict):
        """
        执行双重查询（table_flag为是和否）并合并结果

        :param start_date: 开始日期
        :param end_date: 结束日期
        :param headers: 请求头
        :param cookies_dict: cookies字典
        :return: 合并后的完整数据
        """
        try:
            # 查询配置
            query_configs = [
                {
                    "name": "表格显示-是",
                    "table_flag": TableFlag.YES.value,
                    "description": "获取表格显示为'是'的数据"
                },
                {
                    "name": "表格显示-否",
                    "table_flag": TableFlag.NO.value,
                    "description": "获取表格显示为'否'的数据"
                }
            ]

            all_merged_data = []
            total_records_sum = 0

            # 执行两个查询
            for i, config in enumerate(query_configs, 1):
                self.login_progress.emit(f"🔍 开始第{i}个查询：{config['name']} ({config['description']})")

                # 创建查询参数
                encoded_params = self.create_custom_query(
                    start_date=start_date,
                    end_date=end_date,
                    table_flag=config['table_flag'],
                    ie_flag=IEFlag.EXPORT.value,
                    etps_category=EtpsCategory.COMPANY.value
                )

                # 执行分页查询
                query_data = self.execute_paginated_query(
                    encoded_params, headers, cookies_dict, config['name']
                )

                if query_data:
                    query_records = len(query_data)
                    total_records_sum += query_records
                    all_merged_data.extend(query_data)
                    self.login_progress.emit(f"✅ {config['name']} 查询完成，获取 {query_records} 条记录")
                else:
                    self.login_progress.emit(f"⚠️ {config['name']} 查询无数据")

                # 查询间隔
                if i < len(query_configs):
                    self.login_progress.emit("⏳ 等待1秒后进行下一个查询...")
                    time.sleep(1)

            # 构建最终结果
            if all_merged_data:
                final_result = {
                    "total": str(total_records_sum),
                    "rows": all_merged_data
                }

                self.login_progress.emit(f"🎉 双重查询完成！总计获取 {total_records_sum} 条记录")
                return final_result
            else:
                self.login_progress.emit("❌ 双重查询均无数据返回")
                return None

        except Exception as e:
            self.login_progress.emit(f"❌ 双重查询执行失败: {str(e)}")
            return None

    def execute_paginated_query(self, encoded_params, headers, cookies_dict, query_name):
        """
        执行单个查询的分页获取

        :param encoded_params: 编码后的查询参数
        :param headers: 请求头
        :param cookies_dict: cookies字典
        :param query_name: 查询名称（用于日志）
        :return: 该查询的所有数据列表
        """
        try:
            all_data = []
            page_size = 50  # 每页获取50条数据
            offset = 0
            total_records = None

            while True:
                # 设置请求参数
                params = {
                    'limit': str(page_size),
                    'offset': str(offset),
                    'stName': 'updateTime',
                    'stOrder': 'desc',
                    'decStatusInfo': encoded_params,
                    '_': str(int(datetime.now().timestamp() * 1000)),  # 当前时间戳
                }

                # 发送请求
                page_num = offset // page_size + 1
                self.login_progress.emit(f"  📄 {query_name} - 第{page_num}页（偏移量: {offset}）")

                response = requests.get(
                    'https://sz.singlewindow.cn/dyck/swProxy/decserver/sw/dec/merge/cusQueryNew',
                    params=params,
                    headers=headers,
                    cookies=cookies_dict,
                    timeout=30
                )

                if response.status_code != 200:
                    self.login_progress.emit(f"  ❌ {query_name} - 第{page_num}页获取失败，状态码: {response.status_code}")
                    break

                try:
                    page_data = json.loads(response.text)

                    # 获取总记录数（第一次请求时）
                    if total_records is None:
                        total_records = int(page_data.get('total', 0))
                        if total_records > 0:
                            self.login_progress.emit(f"  📊 {query_name} - 发现 {total_records} 条记录")
                        else:
                            self.login_progress.emit(f"  📊 {query_name} - 无数据")
                            break

                    # 获取当前页的数据
                    current_rows = page_data.get('rows', [])
                    if not current_rows:
                        self.login_progress.emit(f"  📄 {query_name} - 当前页无数据，停止获取")
                        break

                    # 添加到总数据中
                    all_data.extend(current_rows)

                    current_count = len(all_data)
                    self.login_progress.emit(f"  ✅ {query_name} - 第{page_num}页成功，已获取 {current_count}/{total_records} 条")

                    # 检查是否已获取所有数据
                    if current_count >= total_records or len(current_rows) < page_size:
                        break

                    # 准备下一页
                    offset += page_size

                    # 添加短暂延迟，避免请求过于频繁
                    time.sleep(0.3)

                except json.JSONDecodeError as e:
                    self.login_progress.emit(f"  ❌ {query_name} - 第{page_num}页数据解析失败: {str(e)}")
                    break
                except Exception as e:
                    self.login_progress.emit(f"  ❌ {query_name} - 第{page_num}页处理失败: {str(e)}")
                    break

            return all_data

        except Exception as e:
            self.login_progress.emit(f"❌ {query_name} - 分页查询执行失败: {str(e)}")
            return []

    def cleanup_browser_session(self, page):
        """清理浏览器session和临时目录"""
        try:
            self.login_progress.emit("正在清理浏览器session...")

            if page:
                # 获取用户数据目录路径（如果可能的话）
                temp_dir = None
                try:
                    # 尝试获取用户数据目录
                    if hasattr(page, 'driver') and hasattr(page.driver, 'options'):
                        for arg in page.driver.options.arguments:
                            if arg.startswith('--user-data-dir='):
                                temp_dir = arg.replace('--user-data-dir=', '')
                                break
                except:
                    pass

                # 关闭浏览器
                try:
                    page.quit()
                    self.login_progress.emit("✅ 浏览器已关闭")
                except Exception as e:
                    self.login_progress.emit(f"⚠️ 关闭浏览器时出错: {str(e)}")

                # 清理临时目录
                if temp_dir:
                    try:
                        import shutil
                        import os
                        if os.path.exists(temp_dir):
                            shutil.rmtree(temp_dir, ignore_errors=True)
                            self.login_progress.emit(f"✅ 已清理临时目录: {temp_dir}")
                    except Exception as e:
                        self.login_progress.emit(f"⚠️ 清理临时目录时出错: {str(e)}")

            self.login_progress.emit("🔒 账号session已完全清理，确保账号间隔离")

        except Exception as e:
            self.login_progress.emit(f"⚠️ 清理过程中出错: {str(e)}")


class LoginWindow(QMainWindow):
    """登录窗口主类"""
    
    def __init__(self):
        super().__init__()
        self.account_manager = AccountManager()
        self.login_thread = None
        self.init_ui()
        self.load_accounts_to_combo()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("账号管理与登录系统")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：账号管理区域
        left_widget = self.create_account_management_area()
        splitter.addWidget(left_widget)
        
        # 右侧：日志区域
        right_widget = self.create_log_area()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
        
        # 设置样式
        self.setStyleSheet(self.get_stylesheet())
    
    def create_account_management_area(self):
        """创建账号管理区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 账号选择组
        account_group = QGroupBox("账号选择")
        account_layout = QFormLayout(account_group)
        
        self.account_combo = QComboBox()
        self.account_combo.currentTextChanged.connect(self.on_account_selected)
        account_layout.addRow("选择账号:", self.account_combo)
        
        layout.addWidget(account_group)
        
        # 账号信息组
        info_group = QGroupBox("账号信息")
        info_layout = QFormLayout(info_group)

        self.account_name_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)

        # 日期选择器
        self.start_date_edit = QDateEdit()
        self.end_date_edit = QDateEdit()

        # 设置默认日期为前一个星期五及其前六天
        start_date_str, end_date_str = getsetime()
        default_start_date = QDate.fromString(start_date_str, "yyyy-MM-dd")
        default_end_date = QDate.fromString(end_date_str, "yyyy-MM-dd")

        self.start_date_edit.setDate(default_start_date)
        self.start_date_edit.setCalendarPopup(True)

        self.end_date_edit.setDate(default_end_date)
        self.end_date_edit.setCalendarPopup(True)

        # 文件保存路径设置
        self.save_path_edit = QLineEdit()
        self.save_path_edit.setPlaceholderText("选择文件保存路径...")
        self.save_path_edit.setText(os.getcwd())  # 默认为当前目录
        self.save_path_edit.setReadOnly(True)

        self.browse_path_button = QPushButton("浏览...")
        self.browse_path_button.clicked.connect(self.browse_save_path)
        self.browse_path_button.setMaximumWidth(80)

        info_layout.addRow("账号名称:", self.account_name_edit)
        info_layout.addRow("密码:", self.password_edit)
        info_layout.addRow("开始日期:", self.start_date_edit)
        info_layout.addRow("结束日期:", self.end_date_edit)

        # 文件保存路径行
        path_layout = QHBoxLayout()
        path_layout.addWidget(self.save_path_edit)
        path_layout.addWidget(self.browse_path_button)
        path_widget = QWidget()
        path_widget.setLayout(path_layout)
        info_layout.addRow("保存路径:", path_widget)
        
        layout.addWidget(info_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存账号")
        self.save_button.clicked.connect(self.save_account)
        
        self.delete_button = QPushButton("删除账号")
        self.delete_button.clicked.connect(self.delete_account)
        
        self.clear_button = QPushButton("清空表单")
        self.clear_button.clicked.connect(self.clear_form)
        
        self.login_button = QPushButton("登录")
        self.login_button.clicked.connect(self.login_account)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addWidget(self.login_button)
        
        layout.addLayout(button_layout)
        
        # 添加弹性空间
        layout.addStretch()
        
        return widget
    
    def create_log_area(self):
        """创建日志区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 日志组
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # 设置最大文档大小来限制内容
        self.log_text.document().setMaximumBlockCount(1000)
        log_layout.addWidget(self.log_text)
        
        # 清空日志按钮
        clear_log_button = QPushButton("清空日志")
        clear_log_button.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_button)
        
        layout.addWidget(log_group)
        
        return widget
    
    def load_accounts_to_combo(self):
        """加载账号到下拉框"""
        self.account_combo.clear()
        self.account_combo.addItem("-- 选择账号 --")

        account_names = self.account_manager.get_account_names()
        for name in account_names:
            self.account_combo.addItem(name)

        # 默认选择"西格玛"账号
        if "西格玛" in account_names:
            index = self.account_combo.findText("西格玛")
            if index >= 0:
                self.account_combo.setCurrentIndex(index)
                self.add_log(f"已加载 {len(account_names)} 个账号，默认选择：西格玛")
            else:
                self.add_log(f"已加载 {len(account_names)} 个账号")
        else:
            self.add_log(f"已加载 {len(account_names)} 个账号，未找到西格玛账号")
    
    def on_account_selected(self, account_name):
        """账号选择事件"""
        if account_name == "-- 选择账号 --" or not account_name:
            self.clear_form()
            return

        account = self.account_manager.get_account(account_name)
        if account:
            self.account_name_edit.setText(account_name)
            self.password_edit.setText(account.get('password', ''))

            # 始终使用默认日期，不管账号中是否保存了日期
            default_start_str, default_end_str = getsetime()
            default_start_date = QDate.fromString(default_start_str, "yyyy-MM-dd")
            default_end_date = QDate.fromString(default_end_str, "yyyy-MM-dd")

            # 设置为默认日期
            self.start_date_edit.setDate(default_start_date)
            self.end_date_edit.setDate(default_end_date)

            self.add_log(f"已选择账号: {account_name}")
    
    def save_account(self):
        """保存账号"""
        account_name = self.account_name_edit.text().strip()
        password = self.password_edit.text().strip()

        # 获取当前界面上的日期
        current_start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        current_end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # 获取默认日期范围
        default_start_date, default_end_date = getsetime()

        # 如果当前日期是默认日期，或者是新账号，则使用默认日期
        # 这样确保所有账号都有一致的默认日期范围
        start_date = default_start_date
        end_date = default_end_date

        if not account_name:
            QMessageBox.warning(self, "警告", "请输入账号名称！")
            return

        if not password:
            QMessageBox.warning(self, "警告", "请输入密码！")
            return

        # 检查是否是更新现有账号
        if self.account_manager.account_exists(account_name):
            reply = QMessageBox.question(self, "确认", f"账号 '{account_name}' 已存在，是否更新？\n将使用默认日期范围: {start_date} 到 {end_date}",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                success = self.account_manager.update_account_with_dates(
                    account_name, password, start_date, end_date)
                if success:
                    self.add_log(f"✅ 成功更新账号: {account_name} (日期: {start_date} 到 {end_date})")
                    QMessageBox.information(self, "成功", "账号更新成功！")
                else:
                    self.add_log(f"❌ 更新账号失败: {account_name}")
                    QMessageBox.critical(self, "错误", "账号更新失败！")
        else:
            success = self.account_manager.add_account_with_dates(
                account_name, password, start_date, end_date)
            if success:
                self.add_log(f"✅ 成功添加账号: {account_name} (日期: {start_date} 到 {end_date})")
                QMessageBox.information(self, "成功", "账号添加成功！")
                self.load_accounts_to_combo()  # 重新加载下拉框
                # 选择新添加的账号
                index = self.account_combo.findText(account_name)
                if index >= 0:
                    self.account_combo.setCurrentIndex(index)
            else:
                self.add_log(f"❌ 添加账号失败: {account_name}")
                QMessageBox.critical(self, "错误", "账号添加失败！")
    
    def delete_account(self):
        """删除账号"""
        account_name = self.account_name_edit.text().strip()
        
        if not account_name:
            QMessageBox.warning(self, "警告", "请先选择要删除的账号！")
            return
        
        if not self.account_manager.account_exists(account_name):
            QMessageBox.warning(self, "警告", f"账号 '{account_name}' 不存在！")
            return
        
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除账号 '{account_name}' 吗？\n此操作不可撤销！",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            success = self.account_manager.delete_account(account_name)
            if success:
                self.add_log(f"✅ 成功删除账号: {account_name}")
                QMessageBox.information(self, "成功", "账号删除成功！")
                self.load_accounts_to_combo()  # 重新加载下拉框
                self.clear_form()  # 清空表单
            else:
                self.add_log(f"❌ 删除账号失败: {account_name}")
                QMessageBox.critical(self, "错误", "账号删除失败！")
    
    def browse_save_path(self):
        """浏览选择保存路径"""
        try:
            current_path = self.save_path_edit.text()
            if not current_path or not os.path.exists(current_path):
                current_path = os.getcwd()

            selected_path = QFileDialog.getExistingDirectory(
                self,
                "选择文件保存路径",
                current_path,
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )

            if selected_path:
                self.save_path_edit.setText(selected_path)
                self.add_log(f"📁 保存路径已设置为: {selected_path}")

        except Exception as e:
            self.add_log(f"❌ 选择保存路径失败: {str(e)}")
            QMessageBox.warning(self, "警告", f"选择保存路径失败: {str(e)}")

    def get_save_path(self):
        """获取当前设置的保存路径"""
        save_path = self.save_path_edit.text().strip()
        if not save_path or not os.path.exists(save_path):
            save_path = os.getcwd()
            self.save_path_edit.setText(save_path)
        return save_path

    def clear_form(self):
        """清空表单"""
        self.account_name_edit.clear()
        self.password_edit.clear()

        # 设置默认日期为前一个星期五及其前六天
        start_date_str, end_date_str = getsetime()
        default_start_date = QDate.fromString(start_date_str, "yyyy-MM-dd")
        default_end_date = QDate.fromString(end_date_str, "yyyy-MM-dd")

        self.start_date_edit.setDate(default_start_date)
        self.end_date_edit.setDate(default_end_date)
        self.account_combo.setCurrentIndex(0)
    
    def login_account(self):
        """登录账号"""
        account_name = self.account_name_edit.text().strip()
        
        if not account_name:
            QMessageBox.warning(self, "警告", "请先选择要登录的账号！")
            return
        
        account = self.account_manager.get_account(account_name)
        if not account:
            QMessageBox.warning(self, "警告", f"账号 '{account_name}' 不存在！")
            return
        
        # 禁用登录按钮
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")
        
        # 获取日期信息
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # 创建并启动登录线程
        self.login_thread = LoginThread(account, start_date, end_date, account_name)
        self.login_thread.login_success.connect(self.on_login_success)
        self.login_thread.login_failed.connect(self.on_login_failed)
        self.login_thread.login_progress.connect(self.add_log)
        self.login_thread.data_received.connect(self.on_data_received)
        self.login_thread.finished.connect(self.on_login_finished)
        self.login_thread.start()
        
        self.add_log(f"🚀 开始登录账号: {account_name}")
    
    def on_login_success(self, message):
        """登录成功处理"""
        self.add_log(message)
        QMessageBox.information(self, "登录成功", message)
        
        # 更新最后使用时间
        account_name = self.account_name_edit.text().strip()
        self.account_manager.update_last_used(account_name)
    
    def on_login_failed(self, message):
        """登录失败处理"""
        self.add_log(message)
        QMessageBox.critical(self, "登录失败", message)

    def on_data_received(self, data):
        """数据接收处理"""
        try:
            # 解析JSON数据
            json_data = json.loads(data)

            # 格式化显示数据
            formatted_data = json.dumps(json_data, ensure_ascii=False, indent=2)

            # 在日志中显示数据摘要
            rows = json_data.get('rows', [])
            total = json_data.get('total', '0')

            if rows:
                rows_count = len(rows)
                self.add_log(f"📊 获取到 {rows_count} 条记录，总计 {total} 条")

                # 显示前几条记录的摘要
                for i, row in enumerate(rows[:3]):  # 只显示前3条
                    entry_id = row.get('entryId', 'N/A')
                    bill_no = row.get('billNo', 'N/A')
                    consignor = row.get('consignorCname', 'N/A')
                    self.add_log(f"  {i+1}. 海关编号: {entry_id}, 提单号: {bill_no}, 发货人: {consignor}")

                if rows_count > 3:
                    self.add_log(f"  ... 还有 {rows_count - 3} 条记录")

                # 导出到Excel
                self.add_log("📋 正在导出数据到Excel...")
                try:
                    # 获取保存路径
                    save_path = self.get_save_path()
                    excel_filename = export_to_excel(json_data, save_path=save_path)
                    self.add_log(f"✅ Excel导出成功: {excel_filename}")

                    # 显示导出的字段信息
                    self.add_log(f"📄 导出字段数量: {len(FIELD_MAPPING)} 个")
                    self.add_log("📋 导出字段列表:")
                    for chinese_name, field_name in list(FIELD_MAPPING.items())[:5]:  # 显示前5个字段
                        self.add_log(f"  • {chinese_name} ({field_name})")
                    if len(FIELD_MAPPING) > 5:
                        self.add_log(f"  ... 还有 {len(FIELD_MAPPING) - 5} 个字段")

                except Exception as excel_error:
                    self.add_log(f"❌ Excel导出失败: {str(excel_error)}")



            else:
                self.add_log("📊 未获取到数据记录")



        except json.JSONDecodeError as e:
            self.add_log(f"❌ 数据解析失败: {str(e)}")
            self.add_log(f"原始数据: {data[:200]}...")  # 只显示前200个字符
        except Exception as e:
            self.add_log(f"❌ 数据处理失败: {str(e)}")



    def on_login_finished(self):
        """登录完成处理"""
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")
    
    def add_log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("日志已清空")
    
    def get_stylesheet(self):
        """获取样式表"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            background-color: #0084ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #0066cc;
        }
        QPushButton:pressed {
            background-color: #004499;
        }
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        QLineEdit, QComboBox {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }
        QTextEdit {
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: "Consolas", "Monaco", monospace;
        }
        """


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("账号管理与登录系统")
    
    # 设置应用图标（如果有的话）
    # app.setWindowIcon(QIcon('icon.png'))
    
    window = LoginWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
