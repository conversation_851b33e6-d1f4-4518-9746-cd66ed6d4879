#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号管理器
负责处理账号数据的加载、保存和管理
"""

import json
import os
from typing import Dict, List, Optional
from datetime import datetime


class AccountManager:
    """账号管理器类，处理账号数据的加载和保存"""
    
    def __init__(self, json_file: str = "accounts.json"):
        """
        初始化账号管理器
        
        Args:
            json_file: JSON文件路径
        """
        self.json_file = json_file
        self.accounts = {}
        self.load_accounts()
    
    def load_accounts(self) -> bool:
        """
        从JSON文件加载账号信息
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.accounts = data.get('accounts', {})
                print(f"✅ 成功加载 {len(self.accounts)} 个账号")
                return True
            else:
                # 如果文件不存在，创建空的账号字典
                self.accounts = {}
                self.save_accounts()
                print("📝 创建新的账号文件")
                return True
        except Exception as e:
            print(f"❌ 加载账号文件失败: {e}")
            self.accounts = {}
            return False
    
    def save_accounts(self) -> bool:
        """
        保存账号信息到JSON文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            data = {
                'accounts': self.accounts,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 成功保存 {len(self.accounts)} 个账号到 {self.json_file}")
            return True
        except Exception as e:
            print(f"❌ 保存账号文件失败: {e}")
            return False
    
    def add_account(self, account_name: str, username: str, password: str,
                   description: str = "", website: str = "") -> bool:
        """
        添加新账号（保持向后兼容）

        Args:
            account_name: 账号名称（唯一标识）
            username: 用户名
            password: 密码
            description: 账号描述
            website: 网站地址

        Returns:
            bool: 添加是否成功
        """
        if not account_name or not username or not password:
            print("❌ 账号名称、用户名和密码不能为空")
            return False

        self.accounts[account_name] = {
            'username': username,
            'password': password,
            'description': description,
            'website': website,
            'created_time': datetime.now().isoformat(),
            'last_used': None
        }

        if self.save_accounts():
            print(f"✅ 成功添加账号: {account_name}")
            return True
        return False

    def add_account_with_dates(self, account_name: str, password: str,
                              start_date: str = "", end_date: str = "") -> bool:
        """
        添加新账号（带日期）

        Args:
            account_name: 账号名称（唯一标识）
            password: 密码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            bool: 添加是否成功
        """
        if not account_name or not password:
            print("❌ 账号名称和密码不能为空")
            return False

        self.accounts[account_name] = {
            'password': password,
            'start_date': start_date,
            'end_date': end_date,
            'created_time': datetime.now().isoformat(),
            'last_used': None
        }

        if self.save_accounts():
            print(f"✅ 成功添加账号: {account_name}")
            return True
        return False
    
    def update_account(self, account_name: str, username: str = None,
                      password: str = None, description: str = None,
                      website: str = None) -> bool:
        """
        更新账号信息（保持向后兼容）

        Args:
            account_name: 账号名称
            username: 新用户名（可选）
            password: 新密码（可选）
            description: 新描述（可选）
            website: 新网站地址（可选）

        Returns:
            bool: 更新是否成功
        """
        if account_name not in self.accounts:
            print(f"❌ 账号 {account_name} 不存在")
            return False

        account = self.accounts[account_name]

        if username is not None:
            account['username'] = username
        if password is not None:
            account['password'] = password
        if description is not None:
            account['description'] = description
        if website is not None:
            account['website'] = website

        account['updated_time'] = datetime.now().isoformat()

        if self.save_accounts():
            print(f"✅ 成功更新账号: {account_name}")
            return True
        return False

    def update_account_with_dates(self, account_name: str, password: str = None,
                                 start_date: str = None, end_date: str = None) -> bool:
        """
        更新账号信息（带日期）

        Args:
            account_name: 账号名称
            password: 新密码（可选）
            start_date: 新开始日期（可选）
            end_date: 新结束日期（可选）

        Returns:
            bool: 更新是否成功
        """
        if account_name not in self.accounts:
            print(f"❌ 账号 {account_name} 不存在")
            return False

        account = self.accounts[account_name]

        if password is not None:
            account['password'] = password
        if start_date is not None:
            account['start_date'] = start_date
        if end_date is not None:
            account['end_date'] = end_date

        account['updated_time'] = datetime.now().isoformat()

        if self.save_accounts():
            print(f"✅ 成功更新账号: {account_name}")
            return True
        return False
    
    def delete_account(self, account_name: str) -> bool:
        """
        删除账号
        
        Args:
            account_name: 账号名称
            
        Returns:
            bool: 删除是否成功
        """
        if account_name not in self.accounts:
            print(f"❌ 账号 {account_name} 不存在")
            return False
        
        del self.accounts[account_name]
        
        if self.save_accounts():
            print(f"✅ 成功删除账号: {account_name}")
            return True
        return False
    
    def get_account(self, account_name: str) -> Optional[Dict]:
        """
        获取账号信息
        
        Args:
            account_name: 账号名称
            
        Returns:
            Dict: 账号信息，如果不存在返回None
        """
        return self.accounts.get(account_name)
    
    def get_all_accounts(self) -> Dict:
        """
        获取所有账号信息
        
        Returns:
            Dict: 所有账号信息
        """
        return self.accounts.copy()
    
    def get_account_names(self) -> List[str]:
        """
        获取所有账号名称列表
        
        Returns:
            List[str]: 账号名称列表
        """
        return list(self.accounts.keys())
    
    def account_exists(self, account_name: str) -> bool:
        """
        检查账号是否存在
        
        Args:
            account_name: 账号名称
            
        Returns:
            bool: 账号是否存在
        """
        return account_name in self.accounts
    
    def update_last_used(self, account_name: str) -> bool:
        """
        更新账号最后使用时间
        
        Args:
            account_name: 账号名称
            
        Returns:
            bool: 更新是否成功
        """
        if account_name not in self.accounts:
            return False
        
        self.accounts[account_name]['last_used'] = datetime.now().isoformat()
        return self.save_accounts()
    
    def get_account_count(self) -> int:
        """
        获取账号总数
        
        Returns:
            int: 账号总数
        """
        return len(self.accounts)


# 使用示例
if __name__ == "__main__":
    # 创建账号管理器
    manager = AccountManager()
    
    # 添加测试账号
    manager.add_account("测试账号1", "user1", "pass1", "这是测试账号1", "https://example.com")
    manager.add_account("测试账号2", "user2", "pass2", "这是测试账号2", "https://test.com")
    
    # 显示所有账号
    print("\n所有账号:")
    for name in manager.get_account_names():
        account = manager.get_account(name)
        print(f"- {name}: {account['username']} ({account['description']})")
    
    # 更新账号
    manager.update_account("测试账号1", description="更新后的描述")
    
    # 删除账号
    manager.delete_account("测试账号2")
    
    print(f"\n最终账号数量: {manager.get_account_count()}")
