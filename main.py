#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号管理与登录系统 - 主程序入口
整合AccountManager和LoginWindow，提供完整的账号管理和登录功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from login_window import LoginWindow
from account_manager import AccountManager

# 设置Qt平台插件路径（解决平台插件问题）
def fix_qt_plugin_path():
    """修复Qt平台插件路径"""
    import site

    # 获取site-packages路径
    site_packages = site.getsitepackages()

    # 查找PyQt5的Qt插件路径
    for path in site_packages:
        qt_plugins_path = os.path.join(path, 'PyQt5', 'Qt5', 'plugins')
        if os.path.exists(qt_plugins_path):
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = qt_plugins_path
            print(f"✅ 设置Qt插件路径: {qt_plugins_path}")
            return True

    # 如果找不到，尝试其他可能的路径
    try:
        import PyQt5
        pyqt5_path = os.path.dirname(PyQt5.__file__)
        qt_plugins_path = os.path.join(pyqt5_path, 'Qt5', 'plugins')
        if os.path.exists(qt_plugins_path):
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = qt_plugins_path
            print(f"✅ 设置Qt插件路径: {qt_plugins_path}")
            return True

        # 尝试Qt/plugins路径
        qt_plugins_path = os.path.join(pyqt5_path, 'Qt', 'plugins')
        if os.path.exists(qt_plugins_path):
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = qt_plugins_path
            print(f"✅ 设置Qt插件路径: {qt_plugins_path}")
            return True

    except ImportError:
        pass

    print("⚠️ 未找到Qt插件路径，尝试使用默认设置")
    return False

# 修复Qt插件路径
fix_qt_plugin_path()


def check_dependencies():
    """检查依赖包是否安装"""
    missing_packages = []
    
    try:
        import PyQt5
    except ImportError:
        missing_packages.append("PyQt5")
    
    try:
        import DrissionPage
    except ImportError:
        missing_packages.append("DrissionPage")
    
    return missing_packages


def show_dependency_error(missing_packages):
    """显示依赖包缺失错误"""
    app = QApplication(sys.argv)
    
    message = "缺少以下依赖包，请先安装：\n\n"
    for package in missing_packages:
        message += f"pip install {package}\n"
    
    message += "\n完整安装命令：\n"
    message += f"pip install {' '.join(missing_packages)}"
    
    QMessageBox.critical(None, "依赖包缺失", message)
    sys.exit(1)


def main():
    """主函数"""
    print("🚀 启动账号管理与登录系统...")
    
    # 检查依赖包
    missing_packages = check_dependencies()
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        show_dependency_error(missing_packages)
        return
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("账号管理与登录系统")
    app.setApplicationVersion("1.0.0")

    # 设置应用程序属性（在创建QApplication之后）
    try:
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    except:
        # 如果设置失败，继续运行
        pass
    
    try:
        # 创建主窗口
        print("📱 正在创建主界面...")
        window = LoginWindow()
        
        # 显示窗口
        window.show()
        print("✅ 系统启动成功！")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 显示错误对话框
        QMessageBox.critical(None, "启动错误", f"系统启动失败：\n{str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
